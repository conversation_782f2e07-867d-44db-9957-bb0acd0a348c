"""
النافذة الرئيسية - تطبيق إدارة المخازن
Main Window - Desktop Stores Management System
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime
import threading
import time

from config import APP_CONFIG, UI_CONFIG, get_message
from models import User, Item, Beneficiary, Department, Unit
from activity_monitor import ActivityMonitor
from permissions_manager import PermissionsManager, check_permission, can_access

class MainWindow:
    """النافذة الرئيسية للتطبيق"""
    
    def __init__(self, parent, current_user: User, app_instance):
        self.parent = parent
        self.current_user = current_user
        self.app_instance = app_instance

        # متغيرات الحالة
        self.dashboard_data = {}
        self.refresh_timer = None

        # تتبع النوافذ المفتوحة
        self.open_windows = []

        # تتبع النوافذ المفتوحة حسب النوع لمنع التكرار
        self.window_instances = {}
        
        # إعداد النافذة الرئيسية
        self.setup_main_window()
        
        # تحديث البيانات الأولية
        self.refresh_dashboard_data()
        
        # بدء مؤقت التحديث التلقائي
        self.start_auto_refresh()

        # بدء التحديث التلقائي للأنشطة
        self.start_auto_refresh_activities()

        # بدء مراقبة النشاط (من الإعدادات) - فقط إذا كان مفعلاً
        from config import SECURITY_CONFIG
        timeout_minutes = SECURITY_CONFIG.get("auto_logout_minutes", 0)

        # تفعيل مراقب النشاط فقط إذا كان timeout_minutes أكبر من 0
        if timeout_minutes > 0:
            self.activity_monitor = ActivityMonitor(self, app_instance, timeout_minutes=timeout_minutes)
            # تعيين مراقب النشاط العالمي
            from activity_monitor import set_global_activity_monitor
            set_global_activity_monitor(self.activity_monitor)
        else:
            self.activity_monitor = None
    
    def setup_main_window(self):
        """إعداد النافذة الرئيسية"""
        # إعداد النافذة
        self.parent.title(f"{APP_CONFIG['app_name']} - {self.current_user.full_name}")

        # إنشاء شريط الحالة أولاً لتجنب خطأ status_var
        self.create_status_bar()

        # إنشاء القائمة الرئيسية
        self.create_menu_bar()

        # إنشاء شريط الأدوات
        self.create_toolbar()

        # إنشاء المحتوى الرئيسي
        self.create_main_content()
    
    def create_menu_bar(self):
        """إنشاء شريط القوائم"""
        menubar = tk.Menu(self.parent)
        self.parent.config(menu=menubar)
        
        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="🚀 مواصفات وإمكانيات النظام", command=self.show_system_specs)
        file_menu.add_separator()
        file_menu.add_command(label="نسخة احتياطية", command=self.create_backup)
        file_menu.add_command(label="استعادة نسخة احتياطية", command=self.restore_backup)
        file_menu.add_separator()
        file_menu.add_command(label="🔄 تبديل المستخدم", command=self.switch_user)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.exit_application)
        
        # قائمة المستفيدين - فتح مباشر لشاشة إدارة المستفيدين
        if can_access(self.current_user, 'beneficiaries'):
            menubar.add_command(label="المستفيدين", command=self.show_beneficiaries)

        # قائمة الأصناف
        if can_access(self.current_user, 'inventory'):
            items_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="الأصناف", menu=items_menu)
            items_menu.add_command(label="الأصناف", command=self.show_items)
            items_menu.add_command(label="حالة المخزون", command=self.show_inventory_status)
            items_menu.add_command(label="حركة المخزون", command=self.show_inventory_movements)

        # قائمة المخزون - تفتح شاشة لوحة تحكم المخزون مباشرة
        if can_access(self.current_user, 'inventory'):
            menubar.add_command(label="المخزون", command=self.show_inventory_dashboard)
        
        # عمليات الصرف - مباشرة بدون قائمة فرعية
        if can_access(self.current_user, 'transactions'):
            menubar.add_command(label="عمليات الصرف", command=self.show_transactions)
        
        # قائمة التقارير
        if can_access(self.current_user, 'reports'):
            reports_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="التقارير", menu=reports_menu)
            reports_menu.add_command(label="تقرير المخزون", command=self.inventory_report)
            reports_menu.add_command(label="تقرير عمليات الصرف", command=self.transactions_report)
            reports_menu.add_command(label="تقرير المستفيدين", command=self.beneficiaries_report)
            reports_menu.add_separator()
            reports_menu.add_command(label="🔍 البحث في عمليات الصرف", command=self.show_transactions_report)
        
        # قائمة إدارة النظام
        if self.current_user.is_admin:
            admin_menu = tk.Menu(menubar, tearoff=0)
            menubar.add_cascade(label="إدارة النظام", menu=admin_menu)
            admin_menu.add_command(label="المستخدمين", command=self.manage_users)
            admin_menu.add_command(label="الصلاحيات", command=self.manage_permissions)
            admin_menu.add_command(label="الوحدات", command=self.manage_units)
            admin_menu.add_separator()
            admin_menu.add_command(label="الإدارات", command=self.manage_departments)
            admin_menu.add_command(label="الأقسام", command=self.manage_sections)
            admin_menu.add_separator()
            admin_menu.add_command(label="الجدول التنظيمي", command=self.manage_organizational_chart)
            admin_menu.add_separator()
            admin_menu.add_command(label="الإعدادات", command=self.show_settings)
        
        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="🚀 مواصفات وإمكانيات النظام", command=self.show_system_specs)
        help_menu.add_separator()
        help_menu.add_command(label="دليل المستخدم", command=self.show_user_guide)
        help_menu.add_command(label="حول البرنامج", command=self.show_about)
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar_frame = ttk_bs.Frame(self.parent, bootstyle="light")
        toolbar_frame.pack(fill=X, padx=5, pady=5)
        
        # أزرار سريعة مع فحص الصلاحيات
        buttons = [
            ("🏠", "الرئيسية", self.show_dashboard, None),  # متاح للجميع
            ("👥", "المستفيدين", self.show_beneficiaries, 'beneficiaries'),
            ("📦", "المخزون", self.show_inventory_dashboard, 'inventory'),
            ("🔄", "عملية صرف", self.new_transaction, 'transactions'),

            ("📊", "التقارير", self.show_reports, 'reports'),
            ("🔍", "بحث العمليات", self.show_transactions_report, 'reports'),
            ("⚙️", "الإعدادات", self.show_settings, 'settings'),
            ("🚀", "مواصفات النظام", self.show_system_specs, None),  # متاح للجميع
        ]
        
        for icon, text, command, module in buttons:
            # فحص الصلاحيات
            if module is None or can_access(self.current_user, module):
                # تحديد عرض الزر بناءً على طول النص
                text_length = len(text)
                if text_length <= 6:
                    button_width = 10
                elif text_length <= 10:
                    button_width = 12
                elif text_length <= 14:
                    button_width = 15
                else:
                    button_width = 18

                btn = ttk_bs.Button(
                    toolbar_frame,
                    text=f"{icon}\n{text}",
                    command=command,
                    bootstyle="outline-dark",
                    width=button_width
                )
                btn.pack(side=LEFT, padx=2)
        
        # مساحة فارغة
        ttk_bs.Label(toolbar_frame, text="").pack(side=LEFT, expand=True)

        # زر تسجيل الخروج في أقصى اليمين
        logout_btn = ttk_bs.Button(
            toolbar_frame,
            text="🚪 خروج",
            command=self.auto_logout,
            bootstyle="danger",
            width=18
        )
        logout_btn.pack(side=RIGHT, padx=10)
    
    def create_main_content(self):
        """إنشاء المحتوى الرئيسي"""
        # إطار المحتوى الرئيسي
        self.main_frame = ttk_bs.Frame(self.parent)
        self.main_frame.pack(fill=BOTH, expand=True, padx=10, pady=5)
        
        # عرض لوحة التحكم افتراضياً
        self.show_dashboard()
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        # إطار شريط الحالة مع خلفية بيضاء
        status_frame = ttk_bs.Frame(self.parent)
        status_frame.configure(style="White.TFrame")
        status_frame.pack(fill=X, side=BOTTOM)

        # تطبيق ستايل مخصص للخلفية البيضاء
        style = ttk_bs.Style()
        style.configure("White.TFrame", background="white")
        style.configure("StatusLabel.TLabel",
                       background="white",
                       foreground="black",
                       font=("Arial", 10, "bold"))

        # معلومات المستخدم على اليسار
        self.user_status_var = tk.StringVar(value=f"👤 المستخدم: {self.current_user.full_name}")
        ttk_bs.Label(
            status_frame,
            textvariable=self.user_status_var,
            style="StatusLabel.TLabel"
        ).pack(side=LEFT, padx=10, pady=3)

        # معلومات الحالة في الوسط
        self.status_var = tk.StringVar(value="جاهز")
        ttk_bs.Label(
            status_frame,
            textvariable=self.status_var,
            style="StatusLabel.TLabel"
        ).pack(side=LEFT, padx=20, pady=3)

        # الوقت والتاريخ على اليمين
        self.time_var = tk.StringVar()
        ttk_bs.Label(
            status_frame,
            textvariable=self.time_var,
            style="StatusLabel.TLabel"
        ).pack(side=RIGHT, padx=10, pady=3)

        # تحديث الوقت
        self.update_time()
    
    def update_time(self):
        """تحديث الوقت في شريط الحالة"""
        now = datetime.now()
        current_date = now.strftime("%Y-%m-%d")
        current_time = now.strftime("%H:%M:%S")
        self.time_var.set(f"📅 التاريخ: {current_date} | 🕐 الوقت: {current_time}")

        # جدولة التحديث التالي
        self.parent.after(1000, self.update_time)
    
    def clear_main_content(self):
        """مسح المحتوى الرئيسي"""
        for widget in self.main_frame.winfo_children():
            widget.destroy()
    
    def show_dashboard(self):
        """عرض لوحة التحكم"""
        self.clear_main_content()
        # تحديث شريط الحالة إذا كان متوفراً
        if hasattr(self, 'status_var'):
            self.status_var.set("لوحة التحكم")
        
        # عنوان لوحة التحكم
        title_frame = ttk_bs.Frame(self.main_frame)
        title_frame.pack(fill=X, pady=10)
        
        ttk_bs.Label(
            title_frame,
            text="🏠 لوحة التحكم",
            bootstyle="primary"
        ).pack(side=LEFT)
        
        # زر تحديث
        ttk_bs.Button(
            title_frame,
            text="🔄 تحديث",
            command=self.refresh_dashboard_data,
            width=15,
            bootstyle="outline-primary"
        ).pack(side=RIGHT)
        
        # إحصائيات سريعة
        self.create_dashboard_stats()
        
        # الأصناف منخفضة المخزون
        self.create_low_stock_section()
        
        # آخر العمليات
        self.create_recent_activities()
    
    def create_dashboard_stats(self):
        """إنشاء إحصائيات لوحة التحكم"""
        stats_frame = ttk_bs.Frame(self.main_frame)
        stats_frame.pack(fill=X, pady=10)

        # بطاقات الإحصائيات
        stats = [
            ("📦", "إجمالي الأصناف", self.dashboard_data.get("total_items", 0), "primary"),
            ("👥", "إجمالي المستفيدين", self.dashboard_data.get("total_beneficiaries", 0), "success"),
            ("🔄", "عمليات الصرف", self.dashboard_data.get("total_transactions", 0), "info"),
            ("⚠️", "أصناف منخفضة", self.dashboard_data.get("low_stock_count", 0), "warning"),
        ]

        # ترتيب البطاقات من اليمين لليسار
        for i, (icon, title, value, style) in enumerate(reversed(stats)):
            card_frame = ttk_bs.Frame(stats_frame, bootstyle=f"{style}")
            card_frame.pack(side=RIGHT, fill=BOTH, expand=True, padx=5)

            # محتوى البطاقة
            content_frame = ttk_bs.Frame(card_frame, bootstyle="light")
            content_frame.pack(fill=BOTH, expand=True, padx=2, pady=2)

            # الأيقونة مع حجم أكبر
            ttk_bs.Label(
                content_frame,
                text=icon,
                font=("Arial", 24),  # تكبير الأيقونة
                bootstyle=style
            ).pack(pady=8)

            # الرقم مع حجم أكبر وخط عريض
            ttk_bs.Label(
                content_frame,
                text=str(value),
                font=("Arial", 20, "bold"),  # تكبير الرقم وجعله عريض
                bootstyle=style
            ).pack(pady=5)

            # العنوان مع حجم أكبر
            ttk_bs.Label(
                content_frame,
                text=title,
                font=("Arial", 12, "bold"),  # تكبير العنوان وجعله عريض
                bootstyle="secondary"
            ).pack(pady=5)
    
    def create_low_stock_section(self):
        """إنشاء قسم الأصناف منخفضة المخزون"""
        try:
            low_stock_frame = ttk_bs.LabelFrame(
                self.main_frame,
                text="⚠️ أصناف تحتاج إعادة تموين",
                bootstyle="warning"
            )
            low_stock_frame.pack(fill=BOTH, expand=True, pady=10)

            # جدول الأصناف منخفضة المخزون
            columns = ("الصنف", "الكمية الحالية", "الحد الأدنى", "الوحدة")

            # إنشاء Treeview بطريقة آمنة
            try:
                tree = ttk_bs.Treeview(low_stock_frame, columns=columns, show="headings", height=6)

                for col in columns:
                    tree.heading(col, text=col)
                    tree.column(col, width=150)

                # إضافة البيانات
                low_stock_items = self.dashboard_data.get("low_stock_items", [])
                for item in low_stock_items:
                    tree.insert("", "end", values=(
                        item.get("name", ""),
                        item.get("current_quantity", 0),
                        item.get("minimum_quantity", 0),
                        item.get("unit", "")
                    ))

                tree.pack(fill=BOTH, expand=True, padx=10, pady=10)

                # رسالة إذا لم توجد أصناف منخفضة
                if not low_stock_items:
                    ttk_bs.Label(
                        low_stock_frame,
                        text="✅ جميع الأصناف فوق الحد الأدنى",
                        bootstyle="success"
                    ).pack(pady=20)

            except Exception as e:
                # في حالة فشل إنشاء Treeview، عرض رسالة بسيطة بدون تفاصيل الخطأ
                info_label = ttk_bs.Label(
                    low_stock_frame,
                    text="جاري تحميل البيانات...",
                    bootstyle="info"
                )
                info_label.pack(pady=20)
                # تسجيل الخطأ في ملف السجل فقط
                try:
                    from utils.logger import setup_logger
                    logger = setup_logger()
                    logger.debug(f"فشل في إنشاء Treeview: {e}")
                except:
                    pass

        except Exception as e:
            print(f"خطأ في إنشاء قسم الأصناف منخفضة المخزون: {e}")
    
    def create_recent_activities(self):
        """إنشاء قسم الأنشطة الأخيرة مع زر تحديث"""
        activities_frame = ttk_bs.LabelFrame(
            self.main_frame,
            text="📋 آخر الأنشطة",
            bootstyle="info"
        )
        activities_frame.pack(fill=BOTH, expand=True, pady=10)

        # إطار العنوان مع زر التحديث
        header_frame = ttk_bs.Frame(activities_frame)
        header_frame.pack(fill=X, padx=10, pady=(10, 5))

        # زر تحديث الأنشطة
        ttk_bs.Button(
            header_frame,
            text="🔄 تحديث الأنشطة",
            command=self.refresh_recent_activities,
            bootstyle="outline-info",
            width=20
        ).pack(side=RIGHT)

        # قائمة الأنشطة
        self.activities_list = tk.Listbox(activities_frame, height=8)
        self.activities_list.pack(fill=BOTH, expand=True, padx=10, pady=(5, 10))

        # تحميل الأنشطة الأولية
        self.load_recent_activities()

    def load_recent_activities(self):
        """تحميل الأنشطة الأخيرة من قاعدة البيانات"""
        try:
            # مسح القائمة الحالية
            if hasattr(self, 'activities_list'):
                self.activities_list.delete(0, tk.END)

                # تحميل الأنشطة الحقيقية من قاعدة البيانات
                activities = self.get_real_activities()

                if activities:
                    for activity in activities:
                        self.activities_list.insert(tk.END, activity)
                else:
                    # في حالة عدم وجود أنشطة حقيقية، عرض رسالة
                    self.activities_list.insert(tk.END, "📋 لا توجد أنشطة حديثة")

        except Exception as e:
            print(f"خطأ في تحميل الأنشطة الأخيرة: {e}")
            # في حالة الخطأ، عرض رسالة خطأ
            if hasattr(self, 'activities_list'):
                self.activities_list.delete(0, tk.END)
                self.activities_list.insert(tk.END, "❌ خطأ في تحميل الأنشطة")

    def get_real_activities(self):
        """الحصول على الأنشطة الحقيقية من قاعدة البيانات"""
        try:
            from database import db_manager
            activities = []

            # 1. أحدث عمليات الصرف
            transactions_query = """
                SELECT t.transaction_date, t.transaction_number, b.name as beneficiary_name,
                       COUNT(ti.id) as items_count
                FROM transactions t
                LEFT JOIN beneficiaries b ON t.beneficiary_id = b.id
                LEFT JOIN transaction_items ti ON t.id = ti.transaction_id
                WHERE t.status = 'مكتملة'
                GROUP BY t.id
                ORDER BY t.transaction_date DESC, t.id DESC
                LIMIT 5
            """

            transactions = db_manager.fetch_all(transactions_query)
            for trans in transactions:
                try:
                    date_str = trans['transaction_date']
                    if isinstance(date_str, str):
                        from datetime import datetime
                        try:
                            # محاولة تحليل التاريخ مع الوقت الكامل
                            if '.' in date_str:
                                # إزالة الـ microseconds
                                date_str = date_str.split('.')[0]
                            
                            if ' ' in date_str:
                                trans_date = datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
                            else:
                                trans_date = datetime.strptime(date_str, '%Y-%m-%d')
                            
                            time_str = trans_date.strftime('%H:%M')
                            date_display = trans_date.strftime('%m/%d')
                        except ValueError as e:
                            print(f"خطأ في معالجة صيغة المخزون: {e}")
                            time_str = "00:00"
                            date_display = "اليوم"
                    else:
                        time_str = "00:00"
                        date_display = "اليوم"

                    beneficiary = trans['beneficiary_name'] or "غير محدد"
                    items_count = trans['items_count'] or 0

                    activity = f"🔄 {date_display} {time_str} - تم صرف {items_count} صنف للمستفيد {beneficiary}"
                    activities.append(activity)
                except Exception as e:
                    print(f"خطأ في معالجة عملية الصرف: {e}")

            # 2. أحدث حركات المخزون (إضافة)
            movements_query = """
                SELECT im.movement_date, im.movement_type, im.quantity,
                       ai.item_name, im.organization_name
                FROM inventory_movements_new im
                LEFT JOIN added_items ai ON im.item_number = ai.item_number
                WHERE im.is_active = 1 AND im.movement_type = 'إضافة'
                ORDER BY im.movement_date DESC
                LIMIT 3
            """

            movements = db_manager.fetch_all(movements_query)
            for mov in movements:
                try:
                    date_str = mov['movement_date']
                    if isinstance(date_str, str):
                        from datetime import datetime
                        try:
                            # محاولة تحليل التاريخ مع الوقت الكامل
                            if '.' in date_str:
                                # إزالة الـ microseconds
                                date_str = date_str.split('.')[0]
                            
                            mov_date = datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
                            time_str = mov_date.strftime('%H:%M')
                            date_display = mov_date.strftime('%m/%d')
                        except ValueError as e:
                            print(f"خطأ في معالجة صيغة المخزون: {e}")
                            time_str = "00:00"
                            date_display = "اليوم"
                    else:
                        time_str = "00:00"
                        date_display = "اليوم"

                    item_name = mov['item_name'] or "صنف غير محدد"
                    quantity = int(mov['quantity']) if mov['quantity'] else 0

                    activity = f"📦 {date_display} {time_str} - تم إضافة {quantity} قطعة من {item_name} للمخزون"
                    activities.append(activity)
                except Exception as e:
                    print(f"خطأ في معالجة حركة المخزون: {e}")

            # 3. أحدث المستفيدين المضافين
            beneficiaries_query = """
                SELECT name, created_at
                FROM beneficiaries
                WHERE is_active = 1
                ORDER BY created_at DESC
                LIMIT 2
            """

            beneficiaries = db_manager.fetch_all(beneficiaries_query)
            for ben in beneficiaries:
                try:
                    date_str = ben['created_at']
                    if isinstance(date_str, str):
                        from datetime import datetime
                        try:
                            # محاولة تحليل التاريخ مع الوقت الكامل
                            if '.' in date_str:
                                # إزالة الـ microseconds
                                date_str = date_str.split('.')[0]
                            
                            ben_date = datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
                            time_str = ben_date.strftime('%H:%M')
                            date_display = ben_date.strftime('%m/%d')
                        except ValueError as e:
                            print(f"خطأ في معالجة صيغة المخزون: {e}")
                            time_str = "00:00"
                            date_display = "اليوم"
                    else:
                        time_str = "00:00"
                        date_display = "اليوم"

                    name = ben['name'] or "غير محدد"
                    activity = f"👤 {date_display} {time_str} - تم إضافة مستفيد جديد: {name}"
                    activities.append(activity)
                except Exception as e:
                    print(f"خطأ في معالجة المستفيد: {e}")

            # ترتيب الأنشطة حسب التاريخ (الأحدث أولاً)
            return activities[:10]  # أحدث 10 أنشطة

        except Exception as e:
            print(f"خطأ في الحصول على الأنشطة الحقيقية: {e}")
            return []

    def refresh_recent_activities(self):
        """تحديث الأنشطة الأخيرة مع تحديث البيانات"""
        try:
            # تحديث شريط الحالة
            if hasattr(self, 'status_var'):
                self.status_var.set("جاري تحديث الأنشطة والبيانات...")

            # تحديث بيانات لوحة التحكم أولاً
            self.refresh_dashboard_data()

            # إعادة تحميل الأنشطة
            self.load_recent_activities()

            # تحديث شريط الحالة
            if hasattr(self, 'status_var'):
                self.status_var.set("تم تحديث الأنشطة والبيانات بنجاح")

            # إعادة تعيين شريط الحالة بعد 3 ثوان
            self.parent.after(3000, lambda: self.status_var.set("جاهز") if hasattr(self, 'status_var') else None)

            print("✅ تم تحديث الأنشطة والبيانات بنجاح")

        except Exception as e:
            if hasattr(self, 'status_var'):
                self.status_var.set("فشل في تحديث الأنشطة")
            print(f"خطأ في تحديث الأنشطة: {e}")

    def start_auto_refresh_activities(self):
        """بدء التحديث التلقائي للأنشطة كل دقيقة"""
        def auto_refresh_activities():
            try:
                # تحديث الأنشطة فقط (بدون إعادة عرض لوحة التحكم كاملة)
                self.load_recent_activities()
                print("🔄 تم التحديث التلقائي للأنشطة")
            except Exception as e:
                print(f"خطأ في التحديث التلقائي للأنشطة: {e}")

            # جدولة التحديث التالي بعد دقيقة واحدة
            self.parent.after(60000, auto_refresh_activities)  # 60000 مللي ثانية = دقيقة واحدة

        # بدء التحديث التلقائي
        self.parent.after(60000, auto_refresh_activities)  # أول تحديث بعد دقيقة

    def refresh_dashboard_data(self):
        """تحديث بيانات لوحة التحكم"""
        try:
            # استيراد النماذج المطلوبة
            from models import OrganizationalChart, Transaction

            # تحديث الإحصائيات
            self.dashboard_data = {
                "total_items": len(OrganizationalChart.get_all()),  # استخدام الجدول التنظيمي
                "total_beneficiaries": len(Beneficiary.get_all()),
                "total_transactions": len(Transaction.get_all()) if hasattr(Transaction, 'get_all') else 0,
                "low_stock_count": len(Item.get_low_stock_items()) if hasattr(Item, 'get_low_stock_items') else 0,
                "low_stock_items": [
                    {
                        "name": item.name,
                        "current_quantity": item.current_quantity,
                        "minimum_quantity": item.minimum_quantity,
                        "unit": item.unit
                    }
                    for item in (Item.get_low_stock_items() if hasattr(Item, 'get_low_stock_items') else [])
                ]
            }
            
            # تحديث العرض إذا كانت لوحة التحكم مفتوحة
            if hasattr(self, 'main_frame') and self.main_frame.winfo_children():
                self.show_dashboard()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحديث بيانات لوحة التحكم: {e}")
    
    def start_auto_refresh(self):
        """بدء التحديث التلقائي"""
        def auto_refresh():
            while True:
                time.sleep(300)  # تحديث كل 5 دقائق
                try:
                    self.refresh_dashboard_data()
                except:
                    break
        
        refresh_thread = threading.Thread(target=auto_refresh, daemon=True)
        refresh_thread.start()
    
    def reset_activity_timer(self):
        """إعادة تعيين مؤقت النشاط"""
        if hasattr(self, 'activity_monitor') and self.activity_monitor is not None:
            self.activity_monitor.reset_timer()

    def register_window(self, window, window_type=None):
        """تسجيل نافذة مفتوحة للتتبع"""
        if window not in self.open_windows:
            self.open_windows.append(window)
            print(f"📝 تم تسجيل نافذة: {window.title() if hasattr(window, 'title') else 'نافذة غير معروفة'}")

            # تسجيل النافذة حسب النوع لمنع التكرار
            if window_type:
                self.window_instances[window_type] = window
                # ربط حدث الإغلاق لإزالة النافذة من التتبع
                original_destroy = window.destroy
                def on_destroy():
                    if window_type in self.window_instances:
                        del self.window_instances[window_type]
                    self.unregister_window(window)
                    original_destroy()
                window.destroy = on_destroy

    def unregister_window(self, window):
        """إلغاء تسجيل نافذة"""
        if window in self.open_windows:
            self.open_windows.remove(window)
            print(f"❌ تم إلغاء تسجيل نافذة: {window.title() if hasattr(window, 'title') else 'نافذة غير معروفة'}")

    def show_existing_window_or_create_new(self, window_type, create_function):
        """إظهار النافذة الموجودة أو إنشاء جديدة"""
        # التحقق من وجود نافذة مفتوحة من نفس النوع
        if window_type in self.window_instances:
            existing_window = self.window_instances[window_type]
            try:
                # التحقق من أن النافذة ما زالت موجودة
                if existing_window.winfo_exists():
                    # إحضار النافذة للمقدمة
                    existing_window.lift()
                    existing_window.focus_force()
                    print(f"✅ تم إظهار النافذة الموجودة: {window_type}")
                    return True
                else:
                    # النافذة لم تعد موجودة، إزالتها من التتبع
                    del self.window_instances[window_type]
            except:
                # النافذة لم تعد موجودة، إزالتها من التتبع
                if window_type in self.window_instances:
                    del self.window_instances[window_type]

        # إنشاء نافذة جديدة
        create_function()
        return False

    def close_all_registered_windows(self):
        """إغلاق جميع النوافذ المسجلة"""
        closed_count = 0
        windows_to_close = self.open_windows.copy()  # نسخة للتجنب تعديل القائمة أثناء التكرار

        for window in windows_to_close:
            try:
                if window and hasattr(window, 'destroy'):
                    window_title = window.title() if hasattr(window, 'title') else 'نافذة غير معروفة'
                    window.destroy()
                    closed_count += 1
                    print(f"✅ تم إغلاق النافذة: {window_title}")
            except Exception as e:
                print(f"⚠️ خطأ في إغلاق النافذة: {e}")

        # مسح القائمة
        self.open_windows.clear()
        print(f"🎯 تم إغلاق {closed_count} نافذة مسجلة")
        return closed_count

    # دوال الشاشات المفعلة
    def show_beneficiaries(self):
        """عرض شاشة المستفيدين"""
        try:
            self.reset_activity_timer()  # إعادة تعيين المؤقت

            def create_beneficiaries_window():
                from ui.beneficiaries_window import BeneficiariesWindow
                window = BeneficiariesWindow(self.parent, self)
                if hasattr(window, 'window') and window.window:
                    self.register_window(window.window, 'beneficiaries')

            # استخدام النظام الجديد لمنع التكرار
            self.show_existing_window_or_create_new('beneficiaries', create_beneficiaries_window)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح شاشة المستفيدين: {e}")

    def add_beneficiary(self):
        """إضافة مستفيد جديد - توجيه إلى شاشة إدارة المستفيدين"""
        self.show_beneficiaries()

    def show_items(self):
        """عرض شاشة المخزون"""
        try:
            self.reset_activity_timer()  # إعادة تعيين المؤقت

            def create_inventory_window():
                from ui.inventory_window import InventoryWindow
                window = InventoryWindow(self.parent, self)
                if hasattr(window, 'window') and window.window:
                    self.register_window(window.window, 'inventory')

            # استخدام النظام الجديد لمنع التكرار
            self.show_existing_window_or_create_new('inventory', create_inventory_window)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح شاشة المخزون: {e}")

    def add_item(self):
        """إضافة صنف جديد"""
        self.show_items()

    def show_inventory_status(self):
        """عرض شاشة حالة المخزون"""
        try:
            self.reset_activity_timer()  # إعادة تعيين المؤقت
            from ui.inventory_status_window import InventoryStatusWindow
            InventoryStatusWindow(self.parent, self)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح شاشة حالة المخزون: {e}")

    def show_inventory_dashboard(self):
        """عرض لوحة تحكم المخزون"""
        try:
            self.reset_activity_timer()  # إعادة تعيين المؤقت
            from ui.inventory_dashboard_window import InventoryDashboardWindow
            InventoryDashboardWindow(self.parent, self)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح لوحة تحكم المخزون: {e}")

    def show_inventory_movements(self):
        """عرض شاشة حركة المخزون"""
        try:
            self.reset_activity_timer()  # إعادة تعيين المؤقت
            from ui.inventory_movements_window import InventoryMovementsWindow
            InventoryMovementsWindow(self.parent, self)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح شاشة حركة المخزون: {e}")

    def show_transactions(self):
        """عرض شاشة عمليات الصرف في الشاشة الرئيسية"""
        try:
            self.reset_activity_timer()  # إعادة تعيين المؤقت
            from ui.transactions_window import TransactionsWindow
            TransactionsWindow(self.parent, self, embedded=True)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح شاشة عمليات الصرف: {e}")

    def new_transaction(self):
        """إنشاء عملية صرف جديدة"""
        self.show_transactions()
    
    def show_reports(self):
        """عرض شاشة التقارير"""
        try:
            self.reset_activity_timer()  # إعادة تعيين المؤقت

            def create_reports_window():
                from ui.reports_window import ReportsWindow
                window = ReportsWindow(self.parent, self)
                if hasattr(window, 'reports_window') and window.reports_window:
                    self.register_window(window.reports_window, 'reports')

            # استخدام النظام الجديد لمنع التكرار
            self.show_existing_window_or_create_new('reports', create_reports_window)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح شاشة التقارير: {e}")

    def inventory_report(self):
        """تقرير المخزون"""
        self.show_reports()

    def transactions_report(self):
        """تقرير عمليات الصرف"""
        self.show_reports()

    def beneficiaries_report(self):
        """تقرير المستفيدين"""
        self.show_reports()
    
    def manage_users(self):
        """إدارة المستخدمين"""
        # التحقق من الصلاحيات أولاً
        if not can_access(self.current_user, 'users'):
            messagebox.showwarning(
                "غير مسموح", 
                "ليس لديك صلاحية للوصول إلى إدارة المستخدمين"
            )
            return
            
        try:
            from ui.users_management_window import UsersManagementWindow
            UsersManagementWindow(self.parent, self)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح شاشة إدارة المستخدمين: {e}")

    def manage_permissions(self):
        """إدارة الصلاحيات"""
        try:
            from ui.permissions_window import PermissionsWindow
            PermissionsWindow(self.parent, self)
        except Exception as e:
            messagebox.showinfo("قريباً", "إدارة الصلاحيات قيد التطوير")

    def manage_units(self):
        """إدارة الوحدات"""
        try:
            from ui.units_window import UnitsWindow
            UnitsWindow(self.parent, self.current_user)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح شاشة إدارة الوحدات: {e}")

    def manage_departments(self):
        """إدارة الإدارات"""
        try:
            from ui.departments_window import DepartmentsWindow
            DepartmentsWindow(self.parent, self)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح شاشة إدارة الإدارات: {e}")

    def manage_sections(self):
        """إدارة الأقسام"""
        try:
            from ui.sections_window import SectionsWindow
            SectionsWindow(self.parent, self)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح شاشة إدارة الأقسام: {e}")

    def manage_organizational_chart(self):
        """إدارة الجدول التنظيمي"""
        try:
            from ui.organizational_chart_window import OrganizationalChartWindow
            OrganizationalChartWindow(self.parent, self)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح شاشة الجدول التنظيمي: {e}")

    def show_settings(self):
        """عرض شاشة الإعدادات"""
        try:
            self.reset_activity_timer()  # إعادة تعيين المؤقت

            def create_settings_window():
                from ui.settings_window import SettingsWindow
                window = SettingsWindow(self.parent, self)
                if hasattr(window, 'window') and window.window:
                    self.register_window(window.window, 'settings')

            # استخدام النظام الجديد لمنع التكرار
            self.show_existing_window_or_create_new('settings', create_settings_window)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح شاشة الإعدادات: {e}")
    
    def show_system_specs(self):
        """عرض نافذة مواصفات وإمكانيات النظام"""
        try:
            from ui.system_specs_window import SystemSpecsWindow
            SystemSpecsWindow(self.parent)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح نافذة مواصفات النظام: {e}")
    
    def show_user_guide(self):
        """عرض دليل المستخدم"""
        try:
            from ui.user_guide_window import show_user_guide
            show_user_guide(self.parent, self.current_user)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح دليل المستخدم: {e}")
    
    def show_about(self):
        about_text = f"""
{APP_CONFIG['app_name']}
الإصدار {APP_CONFIG['app_version']}

نظام متكامل لإدارة المخازن والمستودعات

تطوير: {APP_CONFIG['app_author']}
للتواصل: {APP_CONFIG['app_contact']}

© 2024 جميع الحقوق محفوظة
        """
        messagebox.showinfo("حول البرنامج", about_text)
    
    def create_backup(self):
        if hasattr(self.app_instance, 'backup_manager'):
            success, message, _ = self.app_instance.backup_manager.create_backup()
            if success:
                messagebox.showinfo("نجح", message)
            else:
                messagebox.showerror("خطأ", message)
        else:
            messagebox.showinfo("قريباً", "النسخ الاحتياطية قيد التطوير")
    
    def restore_backup(self):
        messagebox.showinfo("قريباً", "استعادة النسخ الاحتياطية قيد التطوير")
    
    def logout(self):
        """تسجيل الخروج التلقائي مع حفظ البيانات"""
        self.auto_logout()

    def auto_logout(self):
        """خروج تلقائي مع حفظ البيانات"""
        try:
            print("🔄 بدء عملية الخروج التلقائي...")

            # تحديث شريط الحالة
            if hasattr(self, 'status_var'):
                self.status_var.set("جاري حفظ البيانات والخروج...")

            # حفظ البيانات تلقائياً
            self.auto_save_data()

            # إيقاف مراقب النشاط إذا كان موجوداً
            if hasattr(self, 'activity_monitor') and self.activity_monitor is not None:
                self.activity_monitor.stop_monitoring()

            # إغلاق جميع النوافذ المفتوحة
            self.close_all_registered_windows()

            # تسجيل خروج المستخدم
            if hasattr(self, 'current_user') and self.current_user:
                print(f"👋 تسجيل خروج المستخدم: {self.current_user.full_name}")

            print("✅ تم الخروج بنجاح")

            # إنهاء التطبيق
            self.parent.destroy()

        except Exception as e:
            print(f"❌ خطأ في الخروج التلقائي: {e}")
            # في حالة الخطأ، إنهاء التطبيق مباشرة
            self.parent.destroy()

    def auto_save_data(self):
        """حفظ البيانات تلقائياً قبل الخروج"""
        try:
            print("💾 حفظ البيانات تلقائياً...")

            # حفظ أي بيانات معلقة في النوافذ المفتوحة
            for window in self.open_windows:
                try:
                    if hasattr(window, 'save_pending_data'):
                        window.save_pending_data()
                except:
                    pass

            # حفظ إعدادات النافذة
            try:
                window_geometry = self.parent.geometry()
                print(f"📐 حفظ إعدادات النافذة: {window_geometry}")
            except:
                pass

            # حفظ آخر نشاط للمستخدم
            try:
                if hasattr(self, 'current_user') and self.current_user:
                    from datetime import datetime
                    last_activity = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    print(f"⏰ آخر نشاط: {last_activity}")
            except:
                pass

            print("✅ تم حفظ البيانات بنجاح")

        except Exception as e:
            print(f"⚠️ تحذير: خطأ في حفظ البيانات: {e}")
            # لا نوقف عملية الخروج حتى لو فشل الحفظ

    def switch_user(self):
        """تبديل المستخدم - إغلاق جميع النوافذ والعودة لشاشة تسجيل الدخول"""
        if messagebox.askyesno("تبديل المستخدم", "هل تريد تسجيل الخروج وإغلاق جميع النوافذ والدخول بمستخدم آخر؟"):
            try:
                # استخدام نظام الإغلاق التلقائي لضمان إغلاق جميع النوافذ
                if hasattr(self, 'activity_monitor'):
                    self.activity_monitor.logout_user()
                else:
                    # في حالة عدم وجود مراقب النشاط، استخدم الطريقة التقليدية
                    # تسجيل خروج المستخدم الحالي
                    if self.current_user:
                        self.app_instance.auth_manager.logout(self.current_user)

                    # إخفاء النافذة الرئيسية
                    self.parent.withdraw()

                    # عرض نافذة تسجيل الدخول مرة أخرى
                    self.app_instance.show_login_window()

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تبديل المستخدم: {e}")
    
    def exit_application(self):
        """الخروج من التطبيق"""
        if messagebox.askyesno("تأكيد الخروج", "هل تريد الخروج من التطبيق؟"):
            # إيقاف مراقب النشاط
            if hasattr(self, 'activity_monitor'):
                self.activity_monitor.stop_monitoring()
            self.parent.quit()
    
    def refresh_all_data(self):
        """تحديث جميع البيانات"""
        try:
            print("🔄 تحديث جميع البيانات في النافذة الرئيسية...")

            # تحديث بيانات لوحة التحكم
            self.refresh_dashboard_data()

            # إعادة حساب الكميات إذا لزم الأمر
            try:
                from models import InventoryMovement
                InventoryMovement.recalculate_all_quantities()
                print("✅ تم إعادة حساب الكميات")
            except Exception as e:
                print(f"⚠️ خطأ في إعادة حساب الكميات: {e}")

            # تحديث شريط الحالة إذا كان متوفراً
            if hasattr(self, 'status_var'):
                self.status_var.set("تم تحديث جميع البيانات والكميات")

            print("✅ تم تحديث جميع البيانات بنجاح")

        except Exception as e:
            print(f"❌ خطأ في تحديث البيانات: {e}")
            if hasattr(self, 'status_var'):
                self.status_var.set("خطأ في تحديث البيانات")
    

    

    
    def show_transactions_report(self):
        """عرض تقرير البحث المحسن عن عمليات الصرف"""
        try:
            # التحقق من عدم وجود نافذة مفتوحة بالفعل
            if 'transactions_report' in self.window_instances:
                self.window_instances['transactions_report'].window.lift()
                self.window_instances['transactions_report'].window.focus_force()
                return
            
            from ui.enhanced_transactions_report import EnhancedTransactionsReport
            report_window = EnhancedTransactionsReport(self.parent)
            
            # تتبع النافذة
            self.window_instances['transactions_report'] = report_window
            
            # إزالة النافذة من التتبع عند الإغلاق
            def on_close():
                if 'transactions_report' in self.window_instances:
                    del self.window_instances['transactions_report']
                report_window.window.destroy()
            
            report_window.window.protocol("WM_DELETE_WINDOW", on_close)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح تقرير عمليات الصرف: {str(e)}")
